#ifndef ADOSTYPE_H
#define ADOSTYPE_H

#include <cstdint>

#ifdef _WIN32
//#include <windows.h>
#else
#define __stdcall
#define __int8 char
#define __int16 short
#define __int32 int
#define __int64 long long

#define localtime_s(a, b) localtime_r(b, a)

#include <atomic>
namespace std
{
typedef atomic<uint8_t> atomic_uint8_t ;
typedef atomic<uint16_t> atomic_uint16_t;
typedef atomic<uint32_t> atomic_uint32_t;
}
#endif

#endif // ADOSTYPE_H
