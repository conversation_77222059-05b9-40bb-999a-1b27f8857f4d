#pragma once
#ifndef MOTION_ENUMS_H
#define MOTION_ENUMS_H

#include "ADOSType.h"

/**
 * @brief 资源类型枚举
 * 替代原来的宏定义常量
 */
enum class ResourceType : short {
    None = -1,
    
    LimitPositive = 0,
    LimitNegative = 1,
    Alarm = 2,
    Home = 3,
    GPI = 4,
    Arrive = 5,
    MPG = 6,
    
    Enable = 10,
    Clear = 11,
    GPO = 12,
    
    DAC = 20,
    Step = 21,
    Pulse = 22,
    Encoder = 23,
    ADC = 24,
    
    Axis = 30,
    Profile = 31,
    Control = 32,
    CRD = 33,
    Compare = 34
};

/**
 * @brief 步进模式枚举
 */
enum class StepMode : short {
    Dir = 0,    // 方向模式
    Pulse = 1   // 脉冲模式
};

/**
 * @brief 坐标系停止类型枚举
 */
enum class CrdStopType : short {
    Smooth = 0,  // 平滑停止
    Abrupt = 1   // 急停
};

/**
 * @brief 捕获模式枚举
 */
enum class CaptureMode : short {
    None = 0,
    Home = 1,
    Index = 2,
    Probe = 3,
    HomeIndex = 4
};

/**
 * @brief 捕获状态枚举
 */
enum class CaptureStatus : short {
    None = 0,
    Trigger = 1,
    Set = 2
};

/**
 * @brief 比较模式枚举
 */
enum class CompareMode : short {
    Mode1D = 100,
    Mode2D = 101
};

/**
 * @brief 比较输出类型枚举
 */
enum class CompareOutType : short {
    Pulse = 0,
    Level = 1
};

/**
 * @brief 比较错误枚举
 */
enum class CompareError : short {
    None = 0,
    FpgaFifoEmpty = -1,
    FpgaFifoOverflow = -2,
    DspFifoEmpty = -3,
    DspFifoOverflow = -4,
    NoConfig = -5
};

/**
 * @brief 运动数据类型枚举
 */
enum class ADMotionDataType : short {
    ADMC_CMD = 0,  // @@
    ADMC_IO = 1,   // $$
    ADMC_POS = 2,  // %%
    ADMC_PDO = 3   // ##
};

/**
 * @brief 插补接口指令类型枚举
 */
enum class CrdCmdType : short {
    Line = 0,       // 直线运动
    Arc = 1,        // 圆弧
    Helix = 2,      // 平面螺旋线
    Arc3D = 3,      // 3D圆弧
    Helix3D = 4,    // 3维空间螺旋线
    Bezier = 5,     // Bezier插补
    G0Line = 6,     // G00运动
    FiveAxis = 7,   // 五轴插补
    Jump = 8,       // 点跳
    BufData = 10,   // buf指令数据
    ReturnPausePos = 11  // 返回暂停位置
};

/**
 * @brief 缓冲数据类型枚举
 */
enum class CrdBufDataType : short {
    BufIO = 1,
    BufDA = 2,
    BufDelay = 3,
    BufLmts = 4,
    BufStopIO = 5,
    BufMotion = 6,
    BufTrigger = 7
};

/**
 * @brief 缓冲数据子类型枚举
 */
enum class CrdBufDataSubType : short {
    Move = 0,
    Gear = 1
};

/**
 * @brief 圆弧平面枚举
 */
enum class CrdArcPlane : short {
    XY = 0,
    YZ = 1,
    ZX = 2
};

/**
 * @brief 坐标系变换类型枚举
 */
enum class CrdTransType : short {
    L = 1
};

/**
 * @brief 轴规划模式枚举
 */
enum class PrfMode : short {
    Trap = 0,
    Jog = 1,
    Pt = 2,
    Gear = 3,
    Follow = 4,
    Crd = 5,
    Pvt = 6,
    Home = 7
};

#endif // MOTION_ENUMS_H
