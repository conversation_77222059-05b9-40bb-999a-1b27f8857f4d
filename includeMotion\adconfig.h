﻿#pragma once
#ifndef ADCONFIG_H
#define ADCONFIG_H

////////////////////////////////
//#define CMD_AXIS_ENABLE		0
//#define CMD_GET_AXIS_INFO	1
//#define CMD_SET_CRD_PRM		2
//#define CMD_GET_CRD_PRM		3
//#define CMD_CRD_CLEAR		4
//#define CMD_CRD_START		5
//#define CMD_CRD_STOP		6
//#define CMD_CRD_SINGLE_PAUSE	7
//#define CMD_GET_CRD_STATUS		8
//#define CMD_SEND_CRD_DATA		9
//#define CMD_PRF_TRAP			12
//#define CMD_SET_TRAP_PRM		13
//#define CMD_GET_TRAP_PRM		14
//#define CMD_UPDATE				15
//
//#define CMD_SET_JOG_MODE		16
//#define CMD_SET_JOG_PRM			17
//#define C<PERSON>_JOG_UPDATE			18
//#define	C<PERSON>_GET_POS				19
//
//#define	CMD_SET_AXIS_PRM    20
//#define	CMD_RECORD_ZERO_POS    21
//#define	CMD_SET_ZERO_POS    22
////////////////////////////////////////


#define CMD_SUCCESS                     0

#define CMD_API_ERROR_READ_BLOCK				1
#define CMD_API_ERROR_WRITE_BLOCK				2
#define CMD_API_ERROR_CHANNEL					3
#define CMD_API_ERROR_MTLOCK					5
#define CMD_API_ERROR_OPEN						6
#define CMD_API_ERROR_CLOSE						7
#define CMD_API_ERROR_DSP_BUSY					8
#define CMD_API_ERROR_READ_LEN					9
#define CMD_API_ERROR_READ_CHECKSUM				10

#define CMD_API_ERROR_OUT_RANGE					20//数据范围超限
#define CMD_API_ERROR_TYPE						21//指定类型错误
#define CMD_API_ERROR_POINTER					22//指针类型错误
#define CMD_API_ERROR_PRM						23//参数错误
#define CMD_API_ERROR_RES						24//资源类型错误
#define CMD_API_ERROR_PRF_MODE					25//规划模式错误

#define CMD_API_ERROR_CRD_FIFO_FULL				40//fifo满了
#define CMD_API_ERROR_CRD_TRANS_TYPE			41//贝塞尔类型错误
#define CMD_API_ERROR_CRD_TRANS_DISENABLE		42//贝塞尔功能未使能
#define CMD_API_ERROR_CRD_NO_CONFIG				43//插补坐标系未建立
#define CMD_API_ERROR_CRD_FIFO_HOST_RUNNING		44//主fifo运动时向不能向辅助fifo压入数据
#define CMD_API_ERROR_CRD_LINE_ZERO				45//输入的插补直线长度为0
#define CMD_API_ERROR_CRD_TARGET_Z				46//Jump运动时，Z轴目标位置>Z轴初始位置+h1;
#define CMD_API_ERROR_CRD_ARC_CENTER			47//圆弧圆心坐标错误
#define CMD_API_ERROR_CRD_ARC_END_POS			48//圆弧终点位置错误
#define CMD_API_ERROR_CRD_ARC_RADIUS			49//圆弧半径错误
#define CMD_API_ERROR_CRD_ARC3D_COLLINEAR		50//空间圆弧三点共线
#define CMD_API_ERROR_CRD_ARC3D_RADIUS_SMALL	51//空间圆弧半径过小
#define CMD_API_ERROR_CRD_BEZIER_FIFO1_DISABLE	52//贝塞尔不得向fifo1压入数据
#define CMD_API_ERROR_CRD_FIFO_HOST_NO_PUASE	53//返回运动时，主FIFO未暂停
#define CMD_API_ERROR_CRD_DEMESION				54//低维度坐标系调用了高维度的插补指令

#define CMD_API_ERROR_COMPARE_BUFF_OVERFLOW		60//缓存区溢出
#define CMD_API_ERROR_COMPARE_TRI_VALUE			61//位置比较触发数据错误

#define CMD_API_ERROR_AUTO_TRIG_AXIS_MAP		80
#define CMD_API_ERROR_AUTO_TRIG_MODE			81	
#define CMD_API_ERROR_AUTO_TRIG_OPTION			82
#define CMD_API_ERROR_AUTO_TRIG_CHORD_ZERO		83
#define CMD_API_ERROR_AUTO_TRIG_COUNT_SMALL		84

#define CMD_DSP_ERROR_LOAD      				(-5)
#define CMD_DSP_ERROR_PARSE						(-6)
#define CMD_DSP_ERROR_ADD_RESULT				(-7)

#define CMD_DSP_ERROR_PARAMETER					(-10)//参数错误
#define CMD_DSP_ERROR_AXIS_DISENABLE			(-11)//轴未上使能
#define CMD_DSP_ERROR_AXIS_ENABLE				(-12)//轴使能了
#define CMD_DSP_ERROR_AXIS_RUN					(-13)//轴正在运动
#define CMD_DSP_ERROR_PRF_RUN					(-14)//规划正在运动
#define CMD_DSP_ERROR_AXIS_MAP					(-15)//轴的规划或编码器没有映射
#define CMD_DSP_ERROR_AXIS_STS					(-16)//轴状态异常
#define CMD_DSP_ERROR_PRF_MODE					(-17)//规划模式错误
#define CMD_DSP_ERROR_HOOK						(-18)//有挂接，属于自动输出
#define CMD_DSP_ERROR_PRF_MODE_HOME				(-19)//规划处于回零模式
#define CMD_DSP_ERROR_UNKNOWN					(-20)//未知指令

#define CMD_DSP_ERROR_CRD_HOST_FIFO_RUN			(-30)//当前插补主fifo状态正在运行
#define CMD_DSP_ERROR_CRD_FIFO1_RUN				(-31)//当前插补fifo1状态正在运行
#define CMD_DSP_ERROR_CRD_AXIS_MAP_SAME			(-32)//有多个轴映射相同
#define CMD_DSP_ERROR_CRD_FIFO_OVERFLOW			(-33)//缓存区溢出

#define CMD_DSP_ERROR_HOME_LIMIT_MAP			(-50)//回零的限位映射异常

#define CMD_DSP_ERROR_COMPARE_NO_CONFIG			(-70)//未配置
#define CMD_DSP_ERROR_COMPARE_RUN				(-71)//正在运行

#define CMD_DSP_ERROR_AUTO_TRI_CRD_NOCFG		(-90)//坐标系未配置


//----------------------------------------------------
/** 运控CANOpen 错误码 definitions used for object dictionary access. ie SDO Abort codes . (See DS 301 v.4.02 p.48)
 */
#define OD_SUCCESSFUL 	             0x00000000
#define OD_READ_NOT_ALLOWED          0x06010001
#define OD_WRITE_NOT_ALLOWED         0x06010002
#define OD_NO_SUCH_OBJECT            0x06020000
#define OD_NOT_MAPPABLE              0x06040041
#define OD_LENGTH_DATA_INVALID       0x06070010
#define OD_NO_SUCH_SUBINDEX 	     0x06090011
#define OD_VALUE_RANGE_EXCEEDED      0x06090030 /* Value range test result */
#define OD_VALUE_TOO_LOW             0x06090031 /* Value range test result */
#define OD_VALUE_TOO_HIGH            0x06090032 /* Value range test result */
/* Others SDO abort codes
 */
#define SDOABT_TOGGLE_NOT_ALTERNED   0x05030000
#define SDOABT_TIMED_OUT             0x05040000
#define SDOABT_OUT_OF_MEMORY         0x05040005 /* Size data exceed SDO_MAX_LENGTH_TRANSFER */
#define SDOABT_GENERAL_ERROR         0x08000000 /* Error size of SDO message */
#define SDOABT_LOCAL_CTRL_ERROR      0x08000021

//----------------------------------------------------

#define MAX_LIMIT					12
#define MAX_ALARM					12
#define MAX_HOME					12
#define MAX_GPI						32
#define MAX_ARRIVE					12
#define MAX_MPG						1

#define MAX_ENABLE					12
#define MAX_CLEAR					12
#define MAX_GPO						32

#define MAX_PULSE					12
#define MAX_ENCODER					12
#define MAX_STEP					12
#define MAX_DAC						12
#define MAX_ADC						12

#define MAX_AXIS					8
#define MAX_PROFILE					2
#define MAX_CONTROL                 12

#define MAX_CRD_AXIS				2
#define MAX_CRD						2
#define MAX_CRD_FIFO_COUNT			2
#define MIN_BEZIER_TRANS_PIONT		10//定义贝塞尔过渡时，输入的L参数最少应该能生成10个贝塞尔插补点。

#define MAX_FRAM_BYTE				0x20000

#define  API_CMD_LN_XY						0
#define  API_CMD_LN_XYZ						1
#define  API_CMD_LN_XYZA					2
#define  API_CMD_LN_XYZAB					3

#define  API_CMD_ARC_XY						10
#define  API_CMD_ARC_YZ						11
#define  API_CMD_ARC_ZX						12

#define  API_CMD_TRANS_TYPE_L				20

#define MAX_COMPARE_BUFFER					1024

//--------------------------------
#define TIMEOUT         100
#define FORMATERROR     101
#define CONNECTIONERROR 102
//--------------------------------
extern const wchar_t * GetErrorStr(int ErrCode);
#endif
