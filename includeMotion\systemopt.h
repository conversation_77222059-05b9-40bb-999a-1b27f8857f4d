#ifndef SYSTEMOPT_H
#define SYSTEMOPT_H

#ifdef _WIN32
#include <windows.h>
//#define __int8 char
//#define __int16 short
//#define __int32 int
//#define __int64 long long

#else

#define __stdcall
#define __int8 char
#define __int16 short
#define __int32 int
#define __int64 long long

#endif

struct timeval;
namespace SYSTEMOPT
{
extern void             gettimeofday(timeval* p);
extern unsigned __int64 GetTickCount();
extern void             Sleep(int timeout);
}; // namespace SYSTEMOPT

#endif // SYSTEMOPT_H
