﻿#pragma once
#ifndef ADMC_PCI_H
#define ADMC_PCI_H

// 定义导出宏（如果尚未定义）
#ifndef MC_API_EXPORT
#define MC_API_EXPORT
#endif

#include "ADOSType.h"
#include "motion_types.h"
#include "motion_enums.h"

// 保留这些类型定义以保持向后兼容性
typedef struct TCrdData CrdData;

#if defined(_WIN32) && !defined(__MINGW64__)
#ifdef MC_API_EXPORT
#define MC_API extern "C" __declspec(dllexport)
#else
#define MC_API extern "C" __declspec(dllimport)
#endif
#else
#define MC_API extern "C" __attribute__((visibility("default")))
#endif

class TADMotionConn;

MC_API TADMotionConn* __stdcall API_CreateBoard();                                     //创建卡
MC_API void __stdcall API_DeleteBoard(TADMotionConn* handle);                          //删除卡
MC_API short __stdcall API_OpenBoard(TADMotionConn* handle, const char* ip, int port); //打开卡
MC_API short __stdcall API_CloseBoard(TADMotionConn* handle);                          //关闭卡
MC_API short __stdcall API_ResetBoard(TADMotionConn* handle);                          //复位卡

//设定坐标系对应的xy轴，
MC_API short __stdcall API_SetAxisPrm(TADMotionConn* handle,
                                      short          crd,     // crd对应的坐标系(0-9) 0-4系统保留
                                      short*         AxisMap, // AxisMap Count = 2 : 0=x 1=y ,
                                      short*         AxisDir,
                                      int32_t*       VelMax,
                                      int32_t*       AccMax,
                                      int32_t*       Positive,
                                      int32_t*       Negative);              //设置轴参数
MC_API short __stdcall API_AxisOn(TADMotionConn* handle, short axis);  //使能开
MC_API short __stdcall API_AxisOff(TADMotionConn* handle, short axis); //使能关

MC_API short __stdcall API_SetJogMode(TADMotionConn* handle, short crd);                //设置jog模式
MC_API short __stdcall API_SetJogPrm(TADMotionConn* handle, short crd, TJogPrm* pPrm);  //设置jog参数
MC_API short __stdcall API_XPositive_JogUpdate(TADMotionConn* handle, short crd);       //更新X+运动
MC_API short __stdcall API_XNegative_JogUpdate(TADMotionConn* handle, short crd);       //更新X-运动
MC_API short __stdcall API_YPositive_JogUpdate(TADMotionConn* handle, short crd);       //更新Y+运动
MC_API short __stdcall API_YNegative_JogUpdate(TADMotionConn* handle, short crd);       //更新Y-
MC_API short __stdcall API_New_JogUpdate(TADMotionConn* handle, short axis, short dir); //更新Y-
MC_API short __stdcall API_JogUpdate(TADMotionConn* handle, short crd, short mask);     // 1:x+ 2:x- 3:y+ 4:y- 0:stop

// Crd and Axis Trap
MC_API short __stdcall API_SetCrdTrapMode(TADMotionConn* handle, short crd);                //设置点位模式
MC_API short __stdcall API_SetCrdTrapPrm(TADMotionConn* handle, short crd, TTrapPrm* pPrm); //设置点位参数
MC_API short __stdcall API_CrdTrapUpdate(TADMotionConn* handle, short crd);                 //更新

MC_API short __stdcall API_SetAxisTrapMode(TADMotionConn* handle, short crd, short axis);                //设置点位模式
MC_API short __stdcall API_SetAxisTrapPrm(TADMotionConn* handle, short crd, short axis, TTrapPrm* pPrm); //设置点位参数
MC_API short __stdcall API_AxisTrapUpdate(TADMotionConn* handle, short crd);                             //

MC_API short __stdcall API_RECORD_Home_POS(TADMotionConn* handle, short crd);
MC_API short __stdcall API_GoHome(TADMotionConn* handle, short crd);
MC_API short __stdcall API_AxisGoHome(TADMotionConn* handle, short axis);
MC_API short __stdcall API_AxisClearAlarm(TADMotionConn* handle, short axis);
MC_API short __stdcall API_GetErrorCode(TADMotionConn* handle, short crd, short axis, short* ErrorCode); ////    清理点位
//坐标系
MC_API short __stdcall API_SetCrdPrm(TADMotionConn* handle, short crd, TCrdPrm* pCrdPrm);
MC_API short __stdcall API_SendCrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData); //发送坐标系数据
MC_API short __stdcall API_CrdStart(TADMotionConn* handle, short crd);                        //坐标插补开始
// MC_API short __stdcall API_CrdStop(short mask, short option, short stopType); //坐标插补停止
// MC_API short __stdcall API_CrdPause(short crd, short fifo); //坐标插补暂停
MC_API short __stdcall API_SethomeDir(TADMotionConn* handle,short crd ,short XhomeDir,short YhomeDir);
MC_API short __stdcall API_SetWorkCrd(TADMotionConn* handle,short crd , double XWorkhome,double YWorkhome);

//直线圆弧指令
MC_API short __stdcall API_Ln(
    TADMotionConn* handle, short crd, int32_t x, int32_t y, double synVel, double synAcc, double velEnd);
// MC_API short __stdcall API_LnXYZ(short crd, int32_t x, int32_t y, int32_t z, double synVel, double synAcc, double velEnd);

MC_API short __stdcall API_ArcXY_3point(TADMotionConn* handle,
                                        short          crd,
                                        int32_t*       p1,
                                        int32_t*       p2,
                                        int32_t*       p3,
                                        double         radius,
                                        short          circleDir,
                                        double         synVel,
                                        double         synAcc,
                                        double         velEnd);
MC_API short __stdcall API_ArcXYR(TADMotionConn* handle,
                                  short          crd,
                                  int32_t        x,
                                  int32_t        y,
                                  double         radius,
                                  short          circleDir,
                                  double         synVel,
                                  double         synAcc,
                                  double         velEnd);
MC_API short __stdcall API_ArcXYC(TADMotionConn* handle,
                                  short          crd,
                                  int32_t        x,
                                  int32_t        y,
                                  double         xCenter,
                                  double         yCenter,
                                  short          circleDir,
                                  double         synVel,
                                  double         synAcc,
                                  double         velEnd);
MC_API short __stdcall API_InitLookAhead(TADMotionConn* handle, short crd, double accMax, short count);
MC_API short __stdcall API_CloseLookAhead(TADMotionConn* handle, short crd);

//普通IO
MC_API short __stdcall API_SetDeviceOutput(TADMotionConn* handle, int* deviceOutput);
MC_API short __stdcall API_GetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput);
MC_API short __stdcall API_GetDeviceInput(TADMotionConn* handle, int32_t* deviceInput);

//高速IO
MC_API short __stdcall API_SetSpeedIOParam(TADMotionConn* handle, short io_num, short duty, short period);
MC_API short __stdcall API_SetSpeedIOState(TADMotionConn* handle, short io_num, short switch_state);

//2D点位比较输出(当前安达卡只支持输出到高速IO)
MC_API short __stdcall API_PosCmpEnable(TADMotionConn* handle, short crd, bool bEnable, short error, short frontTime); //// 是否开启位置比较功能
MC_API short __stdcall API_SetPosCmpPoint(
    TADMotionConn* handle, short crd, short seg, double pnt[10240][2], int nPntSize); ////  传输要参与位置比较的点位
MC_API short __stdcall API_ClearCmpPoint(TADMotionConn* handle, short crd);           ////    清理点位
MC_API short __stdcall API_SetPosCmpOutp(TADMotionConn* handle,
    short          crd,
    short            outNum,
    short            outtype,
    long         hlTime_ms,
    long         duty_ms); ////  设置位置比较输出
//--------------
//直接命令操作高速IO电平：
//电平模式支持： io_num ： IO端口 0/1, 返回值 = 0 ，代表运控已成功传输命令到DSP,
//电平模式有效
MC_API short __stdcall API_SetIOPluseEnable(TADMotionConn* handle,short crd, short io_num, short IO_Enable);
//直接输出高或低电平。
MC_API short __stdcall API_SetIOPluseState(TADMotionConn* handle,short crd, short io_num, short IO_State);
//直接翻转电平
MC_API short __stdcall API_SetIOPluseTrigger(TADMotionConn* handle,short crd, short io_num, short IO_Trigger);
//取FPGA版本，version = 260 ，该函数成功上面三个函数才有效。否则无效。
MC_API short __stdcall API_GetFpgaVersion(TADMotionConn* handle,short &version);
//--------------

MC_API short __stdcall API_SendSpeedIO_Point(TADMotionConn* handle, short crd, short pointNum, SpeedIOPoint p[]);
MC_API short __stdcall API_SpeedIO_Enable(TADMotionConn* handle, short crd, short enable, short IO_num);
MC_API short __stdcall API_SpeedIO_ClearPoint(TADMotionConn* handle, short crd);
//------------------------
// CANOpen 支持已移除

//
MC_API short __stdcall API_GetAixsPos(TADMotionConn* handle, short axis, double& pPos);
MC_API short __stdcall API_GetCrdPos(TADMotionConn* handle, short crd, double* pPos);

MC_API short __stdcall API_GetAxisStatus(TADMotionConn* handle, short axis, short& Sts);

MC_API short __stdcall API_DebugModelOption(TADMotionConn* handle, bool);

//MC_API const wchar_t* __stdcall API_GetErrorString(int ErrorCode);


MC_API short __stdcall API_ServoParaSet(TADMotionConn* handle, short station_num, short commandServo, short addr, short data);
//MC_API short __stdcall API_ServoParaGet(TADMotionConn* handle, short station_num, short commandServo, short addr, short& data);
#endif
