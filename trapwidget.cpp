#include "trapwidget.h"
#include "ui_trapwidget.h"
#include <QMessageBox>
#include <QDebug>

TrapWidget::TrapWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TrapWidget),
    m_updateTimer(new QTimer(this)),
    m_currentAxis(0),
    m_currentCrd(0),
    m_isAxisTrapMode(true),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_unitConverter(UnitConverter::getInstance())
{
    ui->setupUi(this);

    // 初始化UI和连接信号槽
    initUI();
    connectSignals();

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &TrapWidget::onUnitTypeChanged);

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &TrapWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout->addWidget(unitGroupBox);

    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();

    // 设置定时器，用于更新状态
    connect(m_updateTimer, &QTimer::timeout, [this]() {
        // 获取当前轴位置
        if (m_apiWrapper->isConnected()) {
            // 坐标系点位模式下更新坐标系位置
            if (ui->tabWidget->currentIndex() == 1) {
                double pos[2] = {0.0, 0.0};
                short result = m_apiWrapper->getCrdPos(m_currentCrd, pos);
                if (result == 0) {
                    // 根据当前单位类型显示位置
                    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
                        ui->lcdCrdPositionX->display(pos[0]);
                        ui->lcdCrdPositionY->display(pos[1]);
                    } else {
                        ui->lcdCrdPositionX->display(m_unitConverter->pulseToMm(pos[0]));
                        ui->lcdCrdPositionY->display(m_unitConverter->pulseToMm(pos[1]));
                    }
                }
            }
        }
    });
    m_updateTimer->start(100); // 100ms更新一次

    // 初始化状态
    updateStatus("就绪");
}

TrapWidget::~TrapWidget()
{
    m_updateTimer->stop();
    delete ui;
}

void TrapWidget::initUI()
{
    // 初始化轴选择下拉框
    for (int i = 0; i < 4; ++i) {
        ui->comboAxis->addItem(QString("轴 %1").arg(i));
    }

    // 初始化坐标系选择下拉框
    for (int i = 0; i < 2; ++i) {
        ui->comboCrdSelect->addItem(QString("坐标系 %1").arg(i));
    }

    // 默认选择轴点位模式
    ui->tabWidget->setCurrentIndex(0);
    m_isAxisTrapMode = true;
}

void TrapWidget::connectSignals()
{
    // 连接Tab切换信号
    connect(ui->tabWidget, &QTabWidget::currentChanged, this, &TrapWidget::onTabChanged);

    // 轴点位模式下的信号连接
    connect(ui->comboAxis, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &TrapWidget::onAxisChanged);
    connect(ui->btnSetAxisTrapParams, &QPushButton::clicked, this, &TrapWidget::setAxisTrapParameters);
    connect(ui->btnStartAxisTrap, &QPushButton::clicked, this, &TrapWidget::startAxisTrapMotion);

    // 坐标系点位模式下的信号连接
    connect(ui->comboCrdSelect, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &TrapWidget::onCrdChanged);
    connect(ui->btnSetCrdTrapParams, &QPushButton::clicked, this, &TrapWidget::setCrdTrapParameters);
    connect(ui->btnStartCrdTrap, &QPushButton::clicked, this, &TrapWidget::startCrdTrapMotion);
}

void TrapWidget::onTabChanged(int index)
{
    // 切换模式
    m_isAxisTrapMode = (index == 0);

    // 更新状态
    if (m_isAxisTrapMode) {
        updateStatus(QString("已切换到轴点位模式"));
    } else {
        updateStatus(QString("已切换到坐标系点位模式"));
    }
}

void TrapWidget::onAxisChanged(int index)
{
    m_currentAxis = index;
    updateStatus(QString("当前选择轴: %1").arg(index));
}

void TrapWidget::onCrdChanged(int index)
{
    m_currentCrd = index;
    updateStatus(QString("当前选择坐标系: %1").arg(index));
}

void TrapWidget::setAxisTrapParameters()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法设置参数");
        return;
    }

    // 从界面获取参数
    double posTarget = ui->spinPosTarget->value();
    double velMax = ui->spinVelMax->value();
    double acc = ui->spinAcc->value();
    short rat = ui->spinRat->value();

    // 获取当前轴位置作为起始位置
    double currentPos = 0.0;
    if (m_apiWrapper->isConnected()) {
        short posResult = m_apiWrapper->getAxisPosition(m_currentAxis, currentPos);
        if (posResult != 0) {
            qDebug() << "Failed to get axis position, using default start position";
        }
    }

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        posTarget = m_unitConverter->mmToPulse(posTarget);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    // 计算增量位置（目标位置 - 当前位置）
    double incrPos = posTarget - currentPos;

    // 设置轴点位模式 - 新接口只需要轴号
    short result = m_apiWrapper->setAxisTrapMode(m_currentAxis);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置轴点位模式失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("设置轴点位模式失败，错误码: %1").arg(result), false);
        return;
    }

    // 设置轴点位参数 - 新接口直接传递参数
    qDebug() << "设置轴点位参数：轴" << m_currentAxis << "增量位置" << incrPos << "最大速度" << velMax << "加速度" << acc << "倍率" << rat;
    result = m_apiWrapper->setAxisTrapParameters(m_currentAxis, incrPos, velMax, acc, rat);
    qDebug() << "设置轴点位参数结果：" << result;
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置轴点位参数失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("设置轴点位参数失败，错误码: %1").arg(result), false);
        return;
    }

    updateStatus(QString("轴%1的点位参数设置成功").arg(m_currentAxis));
    emit apiStatusChanged(QString("轴%1的点位参数设置成功").arg(m_currentAxis), true);
}

void TrapWidget::updateStatus(const QString &message)
{
    ui->labelStatus->setText(message);
}

void TrapWidget::startAxisTrapMotion()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法启动运动");
        return;
    }

    // 启动轴点位运动 - 新接口使用轴号
    short result = m_apiWrapper->axisTrapUpdate(m_currentAxis);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("启动轴点位运动失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("启动轴点位运动失败，错误码: %1").arg(result), false);
        return;
    }

    // 使用当前显示单位的值来显示状态信息
    QString posUnit = m_unitConverter->getPositionUnitString();
    QString posValue;

    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        posValue = QString::number(static_cast<int>(ui->spinPosTarget->value()));
    } else {
        posValue = QString::number(ui->spinPosTarget->value(), 'f', 3);
    }

    QString statusMsg = QString("轴%1的点位运动已启动，目标位置: %2 %3")
                   .arg(m_currentAxis)
                   .arg(posValue)
                   .arg(posUnit);
    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);
}

void TrapWidget::setCrdTrapParameters()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法设置参数");
        return;
    }

    // 从界面获取参数
    double posTargetX = ui->spinPosTargetX->value();
    double posTargetY = ui->spinPosTargetY->value();
    double velMax = ui->spinCrdVelMax->value();
    double acc = ui->spinCrdAcc->value();
    short rat = ui->spinCrdRat->value();

    // 获取当前坐标系位置作为起始位置
//    double startPos[2] = {0.0, 0.0};
//    if (m_apiWrapper->isConnected()) {
//        short posResult = m_apiWrapper->getCrdPos(m_currentCrd, startPos);
//        if (posResult != 0) {
//            qDebug() << "Failed to get coordinate system position, using default start position";
//        }
//    }

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        posTargetX = m_unitConverter->mmToPulse(posTargetX);
        posTargetY = m_unitConverter->mmToPulse(posTargetY);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    // 设置目标位置
    double posTarget[2] = {posTargetX, posTargetY};

    // 设置坐标系点位模式
    short result = m_apiWrapper->setCrdTrapMode(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置坐标系点位模式失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("设置坐标系点位模式失败，错误码: %1").arg(result), false);
        return;
    }

    // 设置坐标系点位参数 - 新接口直接传递参数
    result = m_apiWrapper->setCrdTrapParameters(m_currentCrd, posTarget, velMax, acc, rat);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置坐标系点位参数失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("设置坐标系点位参数失败，错误码: %1").arg(result), false);
        return;
    }

    updateStatus(QString("坐标系%1的点位参数设置成功").arg(m_currentCrd));
    emit apiStatusChanged(QString("坐标系%1的点位参数设置成功").arg(m_currentCrd), true);
}

void TrapWidget::startCrdTrapMotion()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法启动运动");
        return;
    }

    // 启动坐标系点位运动
    short result = m_apiWrapper->crdTrapUpdate(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("启动坐标系点位运动失败，错误码: %1").arg(result));
        emit apiStatusChanged(QString("启动坐标系点位运动失败，错误码: %1").arg(result), false);
        return;
    }

    // 使用当前显示单位的值来显示状态信息
    QString posUnit = m_unitConverter->getPositionUnitString();
    QString posValueX, posValueY;

    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        posValueX = QString::number(static_cast<int>(ui->spinPosTargetX->value()));
        posValueY = QString::number(static_cast<int>(ui->spinPosTargetY->value()));
    } else {
        posValueX = QString::number(ui->spinPosTargetX->value(), 'f', 3);
        posValueY = QString::number(ui->spinPosTargetY->value(), 'f', 3);
    }

    QString statusMsg = QString("坐标系%1的点位运动已启动，目标位置: X=%2 %3, Y=%4 %5")
                   .arg(m_currentCrd)
                   .arg(posValueX)
                   .arg(posUnit)
                   .arg(posValueY)
                   .arg(posUnit);
    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);
}

// 注意：此方法已彻底删除，因为新接口不再使用结构体

// 注意：此方法已彻底删除，因为新接口不再使用结构体

void TrapWidget::onAxisTrapModeSelected()
{
    m_isAxisTrapMode = true;
    updateStatus("已选择轴点位模式");
}

void TrapWidget::onCrdTrapModeSelected()
{
    m_isAxisTrapMode = false;
    updateStatus("已选择坐标系点位模式");
}

void TrapWidget::updateUnitDisplay()
{
    // 获取单位字符串
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    // 更新轴点位模式的单位显示
    ui->labelPosTarget->setText("目标位置(±相对位置):");
    ui->labelVelMax->setText("最大速度:");
    ui->labelAcc->setText("加速度:");
    ui->labelRat->setText("倍率:");

    // 更新轴点位模式的输入框单位
    ui->spinPosTarget->setSuffix(QString(" %1").arg(posUnit));
    ui->spinVelMax->setSuffix(QString(" %1").arg(velUnit));
    ui->spinAcc->setSuffix(QString(" %1").arg(accUnit));
    ui->spinRat->setSuffix(" %");

    // 更新坐标系点位模式的单位显示
    ui->labelPosTargetX->setText("X目标位置:");
    ui->labelPosTargetY->setText("Y目标位置:");
    ui->labelCrdVelMax->setText("最大速度:");
    ui->labelCrdAcc->setText("加速度:");
    ui->labelCrdRat->setText("倍率:");

    // 更新坐标系点位模式的输入框单位
    ui->spinPosTargetX->setSuffix(QString(" %1").arg(posUnit));
    ui->spinPosTargetY->setSuffix(QString(" %1").arg(posUnit));
    ui->spinCrdVelMax->setSuffix(QString(" %1").arg(velUnit));
    ui->spinCrdAcc->setSuffix(QString(" %1").arg(accUnit));
    ui->spinCrdRat->setSuffix(" %");

    // 更新坐标系位置显示的单位
    ui->labelCrdPositionX->setText("X位置:");
    ui->labelCrdPositionY->setText("Y位置:");
    ui->labelCrdUnitX->setText(posUnit);
    ui->labelCrdUnitY->setText(posUnit);
}

void TrapWidget::setDefaultParameters()
{
    // 设置轴点位模式的默认参数值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->spinPosTarget->setValue(0);       // 0 pulse
        ui->spinVelMax->setValue(100);        // 100 pulse/ms
        ui->spinAcc->setValue(10);           // 10 pulse/ms^2
        ui->spinRat->setValue(100);          // 100%
    } else {
        // mm单位下的默认值
        ui->spinPosTarget->setValue(0);       // 0 mm
        ui->spinVelMax->setValue(100);        // 100 mm/s
        ui->spinAcc->setValue(10000);        // 10000 mm/s^2
        ui->spinRat->setValue(100);          // 100%
    }

    // 设置坐标系点位模式的默认参数值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->spinPosTargetX->setValue(0);      // 0 pulse
        ui->spinPosTargetY->setValue(0);      // 0 pulse
        ui->spinCrdVelMax->setValue(100);     // 100 pulse/ms
        ui->spinCrdAcc->setValue(10);        // 10 pulse/ms^2
        ui->spinCrdRat->setValue(100);       // 100%
    } else {
        // mm单位下的默认值
        ui->spinPosTargetX->setValue(0);      // 0 mm
        ui->spinPosTargetY->setValue(0);      // 0 mm
        ui->spinCrdVelMax->setValue(100);     // 100 mm/s
        ui->spinCrdAcc->setValue(10000);     // 10000 mm/s^2
        ui->spinCrdRat->setValue(100);       // 100%
    }
}

void TrapWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();

    // 转换轴点位模式的参数值
    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        ui->spinPosTarget->setValue(ui->spinPosTarget->value() * 1000); // 从 mm 转换到 pulse
        ui->spinVelMax->setValue(ui->spinVelMax->value());             // 速度保持不变
        ui->spinAcc->setValue(ui->spinAcc->value() * 0.001);          // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        ui->spinPosTarget->setValue(ui->spinPosTarget->value() * 0.001); // 从 pulse 转换到 mm
        ui->spinVelMax->setValue(ui->spinVelMax->value());              // 速度保持不变
        ui->spinAcc->setValue(ui->spinAcc->value() * 1000);            // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 转换坐标系点位模式的参数值
    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        ui->spinPosTargetX->setValue(ui->spinPosTargetX->value() * 1000); // 从 mm 转换到 pulse
        ui->spinPosTargetY->setValue(ui->spinPosTargetY->value() * 1000); // 从 mm 转换到 pulse
        ui->spinCrdVelMax->setValue(ui->spinCrdVelMax->value());         // 速度保持不变
        ui->spinCrdAcc->setValue(ui->spinCrdAcc->value() * 0.001);      // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        ui->spinPosTargetX->setValue(ui->spinPosTargetX->value() * 0.001); // 从 pulse 转换到 mm
        ui->spinPosTargetY->setValue(ui->spinPosTargetY->value() * 0.001); // 从 pulse 转换到 mm
        ui->spinCrdVelMax->setValue(ui->spinCrdVelMax->value());          // 速度保持不变
        ui->spinCrdAcc->setValue(ui->spinCrdAcc->value() * 1000);        // 从 pulse/ms^2 转换到 mm/s^2
    }
}

void TrapWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}
