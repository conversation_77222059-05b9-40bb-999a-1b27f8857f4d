﻿#pragma once
#ifndef ADMC_INFO_H
#define ADMC_INFO_H

#include "adconfig.h"
#include "ADOSType.h"
//#define MC_API extern "C" short __stdcall

#define VERSION_DLL  0
#define VERSION_DSP  1
#define VERSION_FPGA 2

//资源类型
#define RES_NONE (-1)

#define RES_LIMIT_POSITIVE 0
#define RES_LIMIT_NEGATIVE 1
#define RES_ALARM          2
#define RES_HOME           3
#define RES_GPI            4
#define RES_ARRIVE         5
#define RES_MPG            6

#define RES_ENABLE 10
#define RES_CLEAR  11
#define RES_GPO    12

#define RES_DAC     20
#define RES_STEP    21
#define RES_PULSE   22
#define RES_ENCODER 23
#define RES_ADC     24

#define RES_AXIS    30
#define RES_PROFILE 31
#define RES_CONTROL 32
#define RES_CRD     33
#define RES_COMPARE 34

#define STEP_DIR   0
#define STEP_PULSE 1

#define CRD_STOP_TYPE_SMOOTH (0)
#define CRD_STOP_TYPE_ABRUPT (1)

#define CAPTURE_MODE_NONE       (0)
#define CAPTURE_MODE_HOME       (1)
#define CAPTURE_MODE_INDEX      (2)
#define CAPTURE_MODE_PROBE      (3)
#define CAPTURE_MODE_HOME_INDEX (4)

#define CAPTURE_STS_NONE    (0)
#define CAPTURE_STS_TRIGGER (1)
#define CAPTURE_STS_SET     (2)

#define COMPARE_MODE_1D (100)
#define COMPARE_MODE_2D (101)

#define COMPARE_OUT_PULSE (0)
#define COMPARE_OUT_LEVEL (1)

#define COMPARE_ERROR_NONE               (0)
#define COMPARE_ERROR_FPGA_FIFO_EMPTY    (-1)
#define COMPARE_ERROR_FPGA_FIFO_OVERFLOW (-2)
#define COMPARE_ERROR_DSP_FIFO_EMPTY     (-3)
#define COMPARE_ERROR_DSP_FIFO_OVERFLOW  (-4)
#define COMPARE_ERROR_NO_CONFIG          (-5)
#define FALSE                            0
#define TRUE                             1
typedef struct
{
    short year;    //版本年份
    short month;   //版本月份
    short day;     //版本日期
    short version; //版本号
    short chip;    //芯片代码
    short reserve1;
    short reserve2;
} TVersion;
typedef struct
{
    short AxisCount;
    short CRDCount;
    short ImportCount;
    short OutportCount;
    short CardType;
    short CardClass;
    char  CardName[32];
    char  CardSN[32];
    char  CardInfo[64];
} TCardInfo; //一张运控卡的相关信息

typedef enum
{
    Trap   = 0,
    Jog    = 1,
    Pt     = 2,
    Gear   = 3,
    Follow = 4,
    Crd    = 5,
    Pvt    = 6,
    Home   = 7
} EPrfMode;

typedef struct TTrapPrm
{
    double acc;    //加速度:pulse/ms^2
    double velMax; //起跳速度:pulse/ms
    short  rat;    //倍率
    double StartPos[MAX_PROFILE];
    double posTarget[MAX_PROFILE];
} TTrapPrm;

typedef struct TJogPrm
{
    int32_t acc;    //加速度:pulse/ms^2  02
    int32_t dec;    //减速度:pulse/ms^2  04
    int32_t Maxvel; //起跳速度:pulse/ms   03
    int32_t rate;   //
} TJogPrm;

typedef struct TCrdPrm
{
    double  synVelMax; // 坐标系内最大合成速度
    double  synAccMax; // 坐标系内最大合成加速度
    short   flagIsZero;
    int32_t originPos[5]; // originPos[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴的原点位置

    short axisMap[5]; // axisMap[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴号。若值为RES_NONE则表示当前轴没有映射
    double axisVelMax[5]; // axisVelMax[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴的最大速度。
    double decSmoothStop; // 坐标系内平滑停止减速度
    double decAbruptStop; // 坐标系内紧急停止减速度
    short  setOriginFlag; // 设置原点坐标值标志,0:默认当前规划位置为原点位置;1:用户指定原点位置

} TCrdPrm;

// Buf操作
typedef struct CrdBufIO
{
    short          type;
    unsigned short doType;
    unsigned short doMask;
    unsigned short doValue;
    short          reserve0;
} TCrdBufIO;
typedef struct CrdBufDA
{
    short type;
    short channel;
    short daValue;
    short reserve0;
    short reserve1;
} TCrdBufDA;
typedef struct CrdBufDelay
{
    short          type;
    unsigned short delayTime;
    short          reserve0;
    short          reserve1;
    short          reserve2;
} TCrdBufDelay;
typedef struct CrdBufLmts
{
    short type;
    short isLmtsOn;
    short axis;
    short limitType;
    short reserve0;
} TCrdBufLmts;
typedef struct CrdBufStopIO
{
    short type;
    short axis;
    short stopType;
    short inputType;
    short inputIndex;
} TCrdBufStopIO;

typedef struct CrdBufMotion
{
    short type;
    short subType;
    short axis;
    short model;
    short reserve0;
} TCrdBufMotion;

typedef struct CrdBufTrigger
{
    short          type;
    unsigned short triCount;
    unsigned short preOffset;
    short          reserve0;
    short          reserve1;
} TCrdBufTrigger;

union BufData
{
    TCrdBufIO      ioData;
    TCrdBufDA      daData;
    TCrdBufDelay   delayData;
    TCrdBufLmts    lmstData;
    TCrdBufStopIO  stopIoData;
    TCrdBufMotion  motionData;
    TCrdBufTrigger triggerData;
};

typedef struct CrdData
{
    short   active[5];
    short   motionType;
    short   circlePlat;
    double  pos[5];
    double  radius;
    short   circleDir;
    double  center[3];
    double  midPoint[3];
    double  vel;
    double  acc;
    double  startVector[3];
    double  endVector[3];
    double  velStart;
    double  velEnd;
    double  velEndAdjust;
    double  length;
    double  crdTrans[9];
    int32_t height;
    double  pitch;
    double  transL;
    double  startPos[5];
    BufData bufData;
} TCrdData;
////////////////////////////////////////////
/// isOpen:是否开阀   openStyle：开阀方式  x/y：胶阀目标点
typedef struct SpeedIOPoint{
    __int64 x;
    __int64 y;
    short isOpen;
    short openStyle;
}TSpeedIOPoint;
#endif
