#include "homewidget.h"
#include "ui_homewidget.h"
#include <QMessageBox>
#include <QTabWidget>
#include <QTimer>

HomeWidget::HomeWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::HomeWidget),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_statusWidget(nullptr),
    m_unitConverter(UnitConverter::getInstance())
{
    // 初始化回零状态和定时器
    for (int i = 0; i < 4; ++i) {
        m_isHoming[i] = false;
        m_statusTimer[i] = new QTimer(this);
        connect(m_statusTimer[i], &QTimer::timeout, [this, i]() {
            checkHomeStatus(i);
        });
        m_statusTimer[i]->setInterval(500); // 每500毫秒检查一次状态
    }

    ui->setupUi(this);

    // 初始化控件
    for (int i = 0; i < 4; ++i) {
        ui->comboAxisSelect->addItem(QString("轴 %1").arg(i));
    }

    // 设置回零方向选项
    ui->comboDirection->addItem("正向");
    ui->comboDirection->addItem("负向");

    // 连接信号槽
    connect(ui->btnStartHome, &QPushButton::clicked, this, &HomeWidget::startHome);
    connect(ui->btnSetParams, &QPushButton::clicked, this, &HomeWidget::setHomeParams);
    connect(ui->comboAxisSelect, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &HomeWidget::onAxisChanged);

    // 创建轴参数初始化模块
    m_axisInitWidget = new AxisInitWidget();

    // 创建轴参数初始化分组框
    QGroupBox* axisInitGroupBox = new QGroupBox("轴参数初始化");
    QVBoxLayout* axisInitLayout = new QVBoxLayout(axisInitGroupBox);
    axisInitLayout->addWidget(m_axisInitWidget);

    // 将轴参数初始化模块添加到主布局
    ui->verticalLayout->insertWidget(4, axisInitGroupBox); // 插入到状态模块之后，占位符之前

    // 连接轴参数初始化模块的信号
    connect(m_axisInitWidget, &AxisInitWidget::apiStatusChanged, this, &HomeWidget::onApiStatusChanged);

    // 创建坐标系轴映射设置模块
    m_axisMappingWidget = new AxisMappingWidget();

    // 创建坐标系轴映射设置分组框
    QGroupBox* axisMappingGroupBox = new QGroupBox("坐标系轴映射设置");
    QVBoxLayout* axisMappingLayout = new QVBoxLayout(axisMappingGroupBox);
    axisMappingLayout->addWidget(m_axisMappingWidget);

    // 将坐标系轴映射设置模块添加到主布局
    ui->verticalLayout->insertWidget(5, axisMappingGroupBox); // 插入到轴参数初始化模块之后

    // 连接坐标系轴映射设置模块的信号
    connect(m_axisMappingWidget, &AxisMappingWidget::apiStatusChanged, this, &HomeWidget::onApiStatusChanged);

    // 状态定时器已在构造函数初始化部分初始化

    // 获取轴状态模块实例
    m_statusWidget = StatusWidget::getInstance();

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &HomeWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout->addWidget(unitGroupBox);

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &HomeWidget::onUnitTypeChanged);
    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();

    // 初始化状态
    updateStatus("就绪");
}

HomeWidget::~HomeWidget()
{
    // 停止所有定时器
    for (int i = 0; i < 4; ++i) {
        if (m_statusTimer[i]->isActive()) {
            m_statusTimer[i]->stop();
        }
    }

    delete ui;
}

void HomeWidget::startHome()
{
    int axis = ui->comboAxisSelect->currentIndex();

    // 只检查当前轴的回零状态
//    if (m_isHoming[axis]) {
//        QMessageBox::warning(this, "警告", QString("轴%1正在执行回零操作，请等待完成或选择其他轴").arg(axis));
//        return;
//    }
    int direction = ui->comboDirection->currentIndex();

    // 获取参数值并根据当前单位进行转换
    double acc = ui->spinAcc->value();
    double dec = ui->spinDec->value();
    double offset = ui->spinOffset->value();

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
        dec = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(dec);
        // 位置需要转换，因为1mm = 1000pulse
        offset = m_unitConverter->mmToPulse(offset);
    }

    // 检查是否已连接
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法执行回零操作");
        emit apiStatusChanged("设备未连接，无法执行回零操作", false);
        return;
    }

    // 调用底层API执行回零操作
    short result = m_apiWrapper->axisGoHome(axis);

    if (result != 0) {
        QString errorMsg = QString("轴%1回零操作失败，错误码: %2").arg(axis).arg(result);
        QMessageBox::warning(this, "错误", errorMsg);
        emit apiStatusChanged(errorMsg, false);
        return;
    }

    // 使用当前显示单位的值来显示状态信息
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString statusMsg = QString("轴%1正在执行回零操作，方向:%2，高速:%3 %4，低速:%5 %6")
                              .arg(axis)
                              .arg(direction == 0 ? "正向" : "负向")
                              .arg(ui->spinHighSpeed->value())
                              .arg(velUnit)
                              .arg(ui->spinLowSpeed->value())
                              .arg(velUnit);

    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);

    // 只更新当前轴的回零状态
    m_isHoming[axis] = true;

    // 启动当前轴的状态检测定时器
    m_statusTimer[axis]->start();
}



void HomeWidget::setHomeParams()
{
    int axis = ui->comboAxisSelect->currentIndex();
    int direction = ui->comboDirection->currentIndex();

    // 获取参数值并根据当前单位进行转换
    double acc = ui->spinAcc->value();
    double dec = ui->spinDec->value();
    double offset = ui->spinOffset->value();

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
        dec = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(dec);
        // 位置需要转换，因为1mm = 1000pulse
        offset = m_unitConverter->mmToPulse(offset);
    }

    // 检查是否已连接
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法设置回零参数");
        emit apiStatusChanged("设备未连接，无法设置回零参数", false);
        return;
    }

    // 注意：API中可能没有直接设置回零参数的方法，这里仅作为示例
    // 实际实现可能需要根据具体API调整

    QString statusMsg = QString("已设置轴%1的回零参数").arg(axis);
    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);

    // 使用当前显示单位的值来显示状态信息
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    QMessageBox::information(this, "参数设置",
                           QString("已设置轴%1的回零参数:\n"
                                  "方向: %2\n"
                                  "高速: %3 %4\n"
                                  "低速: %5 %6\n"
                                  "加速度: %7 %8\n"
                                  "减速度: %9 %10\n"
                                  "偏移量: %11 %12")
                           .arg(axis)
                           .arg(direction == 0 ? "正向" : "负向")
                           .arg(ui->spinHighSpeed->value()).arg(velUnit)
                           .arg(ui->spinLowSpeed->value()).arg(velUnit)
                           .arg(ui->spinAcc->value()).arg(accUnit)
                           .arg(ui->spinDec->value()).arg(accUnit)
                           .arg(ui->spinOffset->value()).arg(posUnit));
}

void HomeWidget::updateStatus(const QString &message)
{
    ui->labelStatus->setText(message);
}

void HomeWidget::onApiStatusChanged(const QString& message, bool isSuccess)
{
    // 更新状态标签
    updateStatus(message);

    // 将信号转发给MainWindow
    emit apiStatusChanged(message, isSuccess);
}

void HomeWidget::onAxisChanged(int axis)
{
    // 当轴变化时，更新状态显示
    QString statusMsg;

    if (m_isHoming[axis]) {
        // 如果切换到的轴正在回零，显示其状态
        statusMsg = QString("轴%1正在执行回零操作").arg(axis);
    } else {
        // 如果切换到的轴没有回零，显示其就绪状态
        statusMsg = QString("轴%1就绪，可以开始回零操作").arg(axis);
    }

    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);
}

void HomeWidget::updateUnitDisplay()
{
    // 更新回零参数的单位显示
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    // 更新回零参数的单位标签
    ui->spinHighSpeed->setSuffix(QString(" %1").arg(velUnit));
    ui->spinLowSpeed->setSuffix(QString(" %1").arg(velUnit));
    ui->spinAcc->setSuffix(QString(" %1").arg(accUnit));
    ui->spinDec->setSuffix(QString(" %1").arg(accUnit));
    ui->spinOffset->setSuffix(QString(" %1").arg(posUnit));

    // 更新轴参数初始化模块的单位显示
    if (m_axisInitWidget) {
        // 如果轴参数初始化模块有更新单位显示的方法，调用它
        // m_axisInitWidget->updateUnitDisplay(unitType);
    }
}

void HomeWidget::setDefaultParameters()
{
    // 设置回零参数的默认值
    // 根据当前单位类型设置默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->spinHighSpeed->setValue(3000); // 3000 pulse/ms
        ui->spinLowSpeed->setValue(100);   // 100 pulse/ms
        ui->spinAcc->setValue(30);         // 30 pulse/ms^2
        ui->spinDec->setValue(30);         // 30 pulse/ms^2
        ui->spinOffset->setValue(0);       // 0 pulse
    } else {
        // mm单位下的默认值
        ui->spinHighSpeed->setValue(3000); // 3000 mm/s
        ui->spinLowSpeed->setValue(100);   // 100 mm/s
        ui->spinAcc->setValue(30000);      // 30000 mm/s^2
        ui->spinDec->setValue(30000);      // 30000 mm/s^2
        ui->spinOffset->setValue(0);       // 0 mm
    }

    // 设置轴参数初始化模块的默认值
    if (m_axisInitWidget) {
        // 如果轴参数初始化模块有设置默认参数的方法，调用它
        // m_axisInitWidget->setDefaultParameters(m_unitConverter->getCurrentUnitType());
    }
}

void HomeWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();

    // 转换当前输入框中的值
    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        ui->spinHighSpeed->setValue(ui->spinHighSpeed->value()); // 速度保持不变
        ui->spinLowSpeed->setValue(ui->spinLowSpeed->value());  // 速度保持不变
        ui->spinAcc->setValue(ui->spinAcc->value() * 0.001);    // 从 mm/s^2 转换到 pulse/ms^2
        ui->spinDec->setValue(ui->spinDec->value() * 0.001);    // 从 mm/s^2 转换到 pulse/ms^2
        ui->spinOffset->setValue(ui->spinOffset->value() * 1000); // 从 mm 转换到 pulse
    } else {
        // 从 pulse 转换到 mm
        ui->spinHighSpeed->setValue(ui->spinHighSpeed->value()); // 速度保持不变
        ui->spinLowSpeed->setValue(ui->spinLowSpeed->value());  // 速度保持不变
        ui->spinAcc->setValue(ui->spinAcc->value() * 1000);     // 从 pulse/ms^2 转换到 mm/s^2
        ui->spinDec->setValue(ui->spinDec->value() * 1000);     // 从 pulse/ms^2 转换到 mm/s^2
        ui->spinOffset->setValue(ui->spinOffset->value() * 0.001); // 从 pulse 转换到 mm
    }
}

void HomeWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}

void HomeWidget::checkHomeStatus(int axis)
{
    // 只有在该轴回零过程中才检查状态
    if (!m_isHoming[axis] || !m_apiWrapper->isConnected()) {
        return;
    }

    // 从轴状态模块获取轴状态
    bool isAtOrigin = false;
    bool isResetting = false;

    if (m_statusWidget) {
        // 使用轴状态模块的方法获取状态
        isAtOrigin = m_statusWidget->isAxisAtOrigin(axis);
        isResetting = m_statusWidget->isAxisResetting(axis);
    } else {
        // 如果轴状态模块不可用，则直接获取
        short status = 0;
        short result = m_apiWrapper->getAxisStatus(axis, status);
        if (result == 0) {
            isAtOrigin = (status & (1 << 3)) != 0; // 原点信号
            isResetting = (status & (1 << 8)) != 0; // 复位中
        }
    }

    // 当到达原点且复位结束时，表示回零完成
    if (isAtOrigin && !isResetting) {
        QString statusMsg = QString("轴%1回零操作完成").arg(axis);
        updateStatus(statusMsg);
        emit apiStatusChanged(statusMsg, true);

        // 重置该轴的回零状态
        m_isHoming[axis] = false;

        // 停止该轴的定时器
        m_statusTimer[axis]->stop();

        // 如果当前显示的是该轴，更新UI状态
        int currentAxis = ui->comboAxisSelect->currentIndex();
        if (currentAxis == axis) {
            updateStatus(statusMsg);
        }
    }
}
