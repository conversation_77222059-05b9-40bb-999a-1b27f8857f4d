#include "admc_api_wrapper.h"
#include <QDebug>

// 错误码定义（从旧接口移植过来）
#define CMD_SUCCESS                 0
#define CMD_API_ERROR_READ_BLOCK   -1
#define CMD_API_ERROR_WRITE_BLOCK  -2
#define CMD_API_ERROR_CHANNEL      -3
#define CMD_API_ERROR_MTLOCK       -4
#define CMD_API_ERROR_OPEN         -5
#define CMD_API_ERROR_CLOSE        -6
#define CMD_API_ERROR_DSP_BUSY     -7
#define CMD_API_ERROR_OUT_RANGE    -8
#define CMD_API_ERROR_TYPE         -9
#define CMD_API_ERROR_POINTER      -10
#define CMD_API_ERROR_PRM          -11
#define CMD_API_ERROR_RES          -12
#define CMD_API_ERROR_PRF_MODE     -13

// 静态实例初始化
AdmcApiWrapper* AdmcApiWrapper::m_instance = nullptr;

AdmcApiWrapper* AdmcApiWrapper::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new AdmcApiWrapper();
    }
    return m_instance;
}

AdmcApiWrapper::AdmcApiWrapper(QObject* parent)
    : QObject(parent), m_handle(nullptr), m_isConnected(false) // 按照声明顺序初始化成员变量
{
    qDebug() << "ADMC API Wrapper initialized";
    m_handle = API_CreateBoard(); // 在构造函数中创建 handle
    if (m_handle == nullptr) {
        qDebug() << "Error: API_CreateBoard failed!"; // 添加错误日志
        // 可以考虑抛出异常或设置错误状态，具体根据项目需求
    }
}

AdmcApiWrapper::~AdmcApiWrapper()
{
    if (m_isConnected) {
        closeBoard();
    }

    // 删除板卡句柄
    if (m_handle != nullptr) {
        deleteBoard();
    }
}

short AdmcApiWrapper::openBoard(const QString& ip, int port)
{
    // 调用底层API连接设备
    if(m_handle == nullptr)
    {
        m_handle = API_CreateBoard();
    }
    if (m_handle == nullptr) return CMD_API_ERROR_OPEN;
    short result = API_OpenBoard(m_handle, ip.toStdString().c_str(), port);

    if (result == 0) { // 成功连接
        m_isConnected = true;
        emit connectionStatusChanged(true);
        qDebug() << "Successfully connected to ADMC board at" << ip << ":" << port;
    } else {
        m_isConnected = false;
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to connect to ADMC board, error code:" << result;
    }

    return result;
}

short AdmcApiWrapper::closeBoard()
{
    if (!m_isConnected) {
        return 0; // 已经断开连接
    }

    // 调用底层API关闭设备
    short result = API_CloseBoard(m_handle);

    if (result == 0) { // 成功断开
        m_isConnected = false;
        emit connectionStatusChanged(false);
        qDebug() << "Successfully disconnected from ADMC board";
    } else {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to disconnect from ADMC board, error code:" << result;
    }

    return result;
}

void AdmcApiWrapper::deleteBoard()
{
    if (m_handle != nullptr) {
        // 调用底层API删除板卡句柄
        API_DeleteBoard(m_handle);
        m_handle = nullptr;
        qDebug() << "Successfully deleted ADMC board handle";
    }
}

short AdmcApiWrapper::resetBoard()
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API复位设备
    short result = API_ResetBoard(m_handle);

    if (result != 0) { // 复位失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to reset ADMC board, error code:" << result;
    } else {
        qDebug() << "Successfully reset ADMC board";
    }

    return result;
}

short AdmcApiWrapper::axisOn(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API使能轴
    short result = API_AxisOn(m_handle, axis);

    if (result != 0) { // 使能失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to enable axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully enabled axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::axisOff(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API禁用轴
    short result = API_AxisOff(m_handle, axis);

    if (result != 0) { // 禁用失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to disable axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully disabled axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::getAxisPosition(short axis, double& position)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取轴位置
    short result = API_GetAixsPos(m_handle, axis, position);

    if (result != 0) { // 获取位置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get position of axis" << axis << ", error code:" << result;
    } else {
        emit axisPositionChanged(axis, position);
    }

    return result;
}

short AdmcApiWrapper::getAxisStatus(short axis, short& status)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取轴状态
    short result = API_GetAxisStatus(m_handle, axis, status);

    if (result != 0) { // 获取状态失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get status of axis" << axis << ", error code:" << result;
    } else {
        emit axisStatusChanged(axis, status);
    }

    return result;
}

short AdmcApiWrapper::getCrdPos(short crd, double* pPos)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取坐标系位置
    short result = API_GetCrdPos(m_handle, crd, pPos);

    if (result != 0) { // 获取位置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get position of coordinate system" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully got position of coordinate system" << crd << ": X=" << pPos[0] << ", Y=" << pPos[1];
    }

    return result;
}

short AdmcApiWrapper::setJogMode(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置Jog模式
    short result = API_SetJogMode(m_handle, crd);
    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Jog mode for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Jog mode for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setJogParameters(short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置Jog参数 - 新接口直接传递参数
    short result = API_SetJogPrm(m_handle, crd, Maxvel, acc, dec, rate);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Jog parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Jog parameters for crd" << crd
                 << " - Maxvel:" << Maxvel << " acc:" << acc << " dec:" << dec << " rate:" << rate;
    }

    return result;
}

short AdmcApiWrapper::jogUpdate(short axis, short dir)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行Jog运动

    short result = API_New_JogUpdate(m_handle, axis, dir);

    if (result != 0) { // Jog运动失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Jog for axis" << axis << "with direction" << dir << ", error code:" << result;
    } else {
        qDebug() << "Successfully updated Jog for axis" << axis << "with direction" << dir;
    }

    return result;
}

short AdmcApiWrapper::setCrdTrapMode(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系点位模式
short result = API_SetCrdTrapMode(m_handle, crd);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Trap mode for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Trap mode for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setCrdTrapParameters(short crd, double posTarget[2], double velMax, double acc, short rat)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系点位参数 - 新接口直接传递参数
    short result = API_SetCrdTrapPrm(m_handle, crd, posTarget, velMax, acc, rat);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Trap parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Trap parameters for crd" << crd
                 << " posTarget:[" << posTarget[0] << "," << posTarget[1] << "]"
                 << " velMax:" << velMax << " acc:" << acc << " rat:" << rat;
    }

    return result;
}

short AdmcApiWrapper::crdTrapUpdate(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API更新坐标系点位运动
    short result = API_CrdTrapUpdate(m_handle, crd);

    if (result != 0) { // 更新失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Trap for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully updated Trap for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setAxisTrapMode(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴点位模式 - 新接口只需要轴号
    short result = API_SetAxisTrapMode(m_handle, axis);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis Trap mode for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis Trap mode for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::setAxisTrapParameters(short axis, double IncrPos, double velMax, double acc, short rat)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴点位参数 - 新接口直接传递参数
    short result = API_SetAxisTrapPrm(m_handle, axis, IncrPos, velMax, acc, rat);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis Trap parameters for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis Trap parameters for axis" << axis
                 << " - IncrPos:" << IncrPos << " velMax:" << velMax
                 << " acc:" << acc << " rat:" << rat;
    }

    return result;
}

short AdmcApiWrapper::axisTrapUpdate(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API更新轴点位运动
    // 注意：新接口中API_AxisTrapUpdate的参数仍然是crd，但实际应该传入对应的坐标系
    // axis = [0,1]时 对应crd = 0  axis = [2,3]时 对应crd = 1
    short crd = axis / 2;
    short result = API_AxisTrapUpdate(m_handle, crd);

    if (result != 0) { // 更新失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Axis Trap for axis" << axis << "(crd" << crd << "), error code:" << result;
    } else {
        qDebug() << "Successfully updated Axis Trap for axis" << axis << "(crd" << crd << ")";
    }

    return result;
}

short AdmcApiWrapper::axisGoHome(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行回零
    short result = API_AxisGoHome(m_handle, axis);

    if (result != 0) { // 回零失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to go home for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully started home operation for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::axisClearAlarm(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API清除报警
    short result = API_AxisClearAlarm(m_handle, axis);

    if (result != 0) { // 清除报警失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to clear alarm for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully cleared alarm for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::getAxisErrorCode(short axis, short& errorCode)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取轴错误码 - 新接口移除了crd参数
    short result = API_GetErrorCode(m_handle, axis, &errorCode);

    if (result != 0) { // 获取错误码失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get error code for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully got error code for axis" << axis << ": " << errorCode;
    }

    return result;
}

QString AdmcApiWrapper::getErrorString(short errorCode)
{
    // 根据错误码返回对应的错误信息
    switch (errorCode) {
        case CMD_SUCCESS:
            return "操作成功";
        case CMD_API_ERROR_READ_BLOCK:
            return "读取数据块错误";
        case CMD_API_ERROR_WRITE_BLOCK:
            return "写入数据块错误";
        case CMD_API_ERROR_CHANNEL:
            return "通道错误，设备未连接";
        case CMD_API_ERROR_MTLOCK:
            return "互斥锁操作错误";
        case CMD_API_ERROR_OPEN:
            return "打开设备失败";
        case CMD_API_ERROR_CLOSE:
            return "关闭设备失败";
        case CMD_API_ERROR_DSP_BUSY:
            return "DSP忙，请稍后再试";
        case CMD_API_ERROR_OUT_RANGE:
            return "数据范围超出限制";
        case CMD_API_ERROR_TYPE:
            return "指定类型错误";
        case CMD_API_ERROR_POINTER:
            return "指针类型错误";
        case CMD_API_ERROR_PRM:
            return "参数错误";
        case CMD_API_ERROR_RES:
            return "资源类型错误";
        case CMD_API_ERROR_PRF_MODE:
            return "规划模式错误";
        default:
            if (errorCode < 0) {
                return QString("DSP错误: %1").arg(errorCode);
            } else {
                return QString("未知错误: %1").arg(errorCode);
            }
    }
}


// 新增：坐标系参数设置
short AdmcApiWrapper::setCrdPrm(short crd, double synVelMax, double synAccMax)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setCrdPrm called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系参数 - 新接口直接传递参数
    short result = API_SetCrdPrm(m_handle, crd, synVelMax, synAccMax);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Crd parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Crd parameters for crd" << crd
                 << " - synVelMax:" << synVelMax << " synAccMax:" << synAccMax;
    }

    return result;
}

// 新增：直线插补
short AdmcApiWrapper::ln(
    short crd,
    int32_t x,
    int32_t y,
    double synVel,
    double synAcc,
    double velEnd
)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ln called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行直线插补 - 新接口移除了velEnd参数
    short result = API_Ln(m_handle, crd, x, y, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute Ln command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed Ln command for crd" << crd
                 << " - x:" << x << " y:" << y << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：圆弧插补
short AdmcApiWrapper::ArcXYR(
    short crd,
    int32_t x,
    int32_t y,
    double radius,
    short circleDir,
    double synVel,
    double synAcc,
    double velEnd
)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ArcXYR called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行圆弧插补 - 新接口移除了velEnd参数
    short result = API_ArcXYR(m_handle, crd, x, y, radius, circleDir, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute ArcXYR command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed ArcXYR command for crd" << crd
                 << " - x:" << x << " y:" << y << " radius:" << radius
                 << " circleDir:" << circleDir << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：启动坐标系插补运动
short AdmcApiWrapper::crdStart(short crd)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "crdStart called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API启动坐标系插补运动
    short result = API_CrdStart(m_handle, crd);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to start coordinate system" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully started coordinate system" << crd;
    }

    return result;
}

// 新增：轴参数初始化
short AdmcApiWrapper::setAxisPrm(short crd, short* axisMap, short* axisDir, int32_t* velMax, int32_t* accMax, int32_t* positive, int32_t* negative)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setAxisPrm called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴参数
    short result = API_SetAxisPrm(m_handle, crd, axisMap, axisDir, velMax, accMax, positive, negative);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis parameters for crd" << crd;
    }

    return result;
}

// 新增：坐标系轴映射
short AdmcApiWrapper::setAxisMapping(short x1, short y1, short x2, short y2)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setAxisMapping called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 注意：新接口中可能没有API_AxisMapping函数，轴映射功能可能已整合到API_SetAxisPrm中
    // 这里暂时返回成功，实际的轴映射应该通过setAxisPrm方法来设置
    qWarning() << "setAxisMapping: 新接口中可能没有独立的轴映射函数，请使用setAxisPrm方法";
    qDebug() << "Axis mapping parameters: x1=" << x1 << ", y1=" << y1 << ", x2=" << x2 << ", y2=" << y2;

    // 返回成功，但实际功能需要通过其他方式实现
    return 0;
}

// 新增：设置设备输出
short AdmcApiWrapper::setDeviceOutput(int* deviceOutput)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setDeviceOutput called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置设备输出
    short result = API_SetDeviceOutput(m_handle, deviceOutput);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set device output, error code:" << result;
    } else {
        qDebug() << "Successfully set device output";
    }

    return result;
}

// 新增：获取设备输入
short AdmcApiWrapper::getDeviceInput(int32_t* deviceInput)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "getDeviceInput called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取设备输入
    short result = API_GetDeviceInput(m_handle, deviceInput);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get device input, error code:" << result;
    }

    return result;
}
